import path from 'path'
import { defineConfig, type UserConfigExport } from '@tarojs/cli'
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin'
import { UnifiedWebpackPluginV5 } from 'weapp-tailwindcss/webpack'
import RemovePlugin from 'remove-files-webpack-plugin'
import { UploadPlugin } from '@msb-next/webpack-plugins'
import WebpackAliyunOss from 'webpack-aliyun-oss'
import devConfig from './dev'
import prodConfig from './prod'

console.log('NODE_ENV:', process.env.NODE_ENV)
console.log('TARO_ENV:', process.env.TARO_ENV)
console.log('TARO_APP_ENV:', process.env.TARO_APP_ENV)

const isBuild = process.env.NODE_ENV === 'production'
const CDN_URL = `https://fe-cdn.xiaoxiongmeishu.com/ai-mp-wode-shop/${process.env.TARO_ENV}/${process.env.TARO_APP_ENV}/`

// https://taro-docs.jd.com/docs/next/config#defineconfig-辅助函数
export default defineConfig(async (merge, {}) => {
  const baseConfig: UserConfigExport = {
    projectName: 'ai-mp-wode-shop',
    date: '2024-3-14',
    designWidth: 750,
    deviceRatio: {
      640: 2.34 / 2,
      750: 1,
      375: 2,
      828: 1.81 / 2
    },
    sourceRoot: 'src',
    outputRoot: 'dist',
    alias: {
      '@': path.resolve(__dirname, '..', 'src')
    },
    plugins: ['@tarojs/plugin-html'],
    defineConstants: {},
    copy: {
      patterns: [],
      options: {}
    },
    framework: 'react',
    compiler: 'webpack5',
    cache: {
      enable: false // Webpack 持久化缓存配置，建议开启。默认配置请参考：https://docs.taro.zone/docs/config-detail#cache
    },
    mini: {
      enableSourceMap: !isBuild, // 是否开启 sourceMap
      postcss: {
        pxtransform: {
          enable: true,
          config: {}
        },
        url: {
          enable: true,
          config: {
            limit: isBuild ? 1 : 1024, // 设定转换尺寸上限
            basePath: isBuild ? CDN_URL : ''
          }
        },
        cssModules: {
          enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
          config: {
            namingPattern: 'module', // 转换模式，取值为 global/module
            generateScopedName: '[name]__[local]___[hash:base64:5]'
          }
        }
      },
      imageUrlLoaderOption: {
        limit: 10,
        name: 'assets/[name].[hash][ext]',
        publicPath: isBuild ? CDN_URL : '/'
      },
      webpackChain(chain) {
        chain.resolve.plugin('tsconfig-paths').use(TsconfigPathsPlugin)
        chain.merge({
          plugin: {
            install: {
              plugin: UnifiedWebpackPluginV5,
              args: [
                {
                  appType: 'taro',
                  // 下面个配置，会开启 rem -> rpx 的转化
                  rem2rpx: true
                }
              ]
            }
          }
        })
        if (isBuild) {
          // chain.plugin('msb-next/webpack-plugins').use(
          //   new UploadPlugin({
          //     base: 'dist/assets',
          //     prefix: `ai-mp-wode-shop/${process.env.TARO_ENV}/${process.env.TARO_APP_ENV}/assets`,
          //     bucket: 'msb-xiaoxiong-fe'
          //   })
          // )
          chain.plugin('webpack-aliyun-oss').use(WebpackAliyunOss, [
            {
              from: ['./dist/assets/**'], //排除html文件
              dist: `ai-mp-wode-shop/${process.env.TARO_ENV}/${process.env.TARO_APP_ENV}`,
              region: 'oss-cn-hangzhou',
              accessKeyId: 'LTAI4GDaQcWnC9KqLgvjKwih',
              accessKeySecret: '******************************',
              bucket: 'msb-xiaoxiong-fe'
            }
          ])
          chain.plugin('remove-files-webpack-plugin').use(RemovePlugin, [{ after: { include: ['dist/assets'], trash: true } }])
        }
      }
    },
    h5: {
      publicPath: '/',
      staticDirectory: 'assets',
      output: {
        filename: 'js/[name].[hash:8].js',
        chunkFilename: 'js/[name].[chunkhash:8].js'
      },
      miniCssExtractPluginOption: {
        ignoreOrder: true,
        filename: 'css/[name].[hash].css',
        chunkFilename: 'css/[name].[chunkhash].css'
      },
      postcss: {
        autoprefixer: {
          enable: true,
          config: {}
        },
        cssModules: {
          enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
          config: {
            namingPattern: 'module', // 转换模式，取值为 global/module
            generateScopedName: '[name]__[local]___[hash:base64:5]'
          }
        }
      },
      imageUrlLoaderOption: {
        limit: 10,
        name: 'assets/[name].[hash][ext]',
        publicPath: isBuild ? CDN_URL : '/'
      },
      devServer: {
        port: 10086,
        proxy: {
          '/api': {
            // target: "http://ai-app-dev.meixiu.mobi",
            target: 'http://dev.meixiu.mobi',
            changeOrigin: true
            // pathRewrite: {
            //   "^/api": "",
            // },
          }
          // "/api/tv": {
          //   target: "http://192.168.124.75:8080",
          //   changeOrigin: true,
          //   pathRewrite: {
          //     "^/api/tv": "",
          //   },
          // },
        }
      },
      webpackChain(chain) {
        chain.resolve.plugin('tsconfig-paths').use(TsconfigPathsPlugin)
        if (isBuild) {
          chain.plugin('msb-next/webpack-plugins').use(
            new UploadPlugin({
              prefix: `ai-mp-wode-shop/${process.env.TARO_ENV}/${process.env.TARO_APP_ENV}`,
              bucket: 'msb-xiaoxiong-fe'
            })
          )
        }
      }
    },
    rn: {
      appName: 'taroDemo',
      postcss: {
        cssModules: {
          enable: false // 默认为 false，如需使用 css modules 功能，则设为 true
        }
      }
    }
  }
  if (!isBuild) {
    // 本地开发构建配置（不混淆压缩）
    return merge({}, baseConfig, devConfig)
  }
  // 生产构建配置（默认开启压缩混淆等）
  return merge({}, baseConfig, prodConfig)
})
