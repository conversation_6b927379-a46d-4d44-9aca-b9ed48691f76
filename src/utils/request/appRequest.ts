import Taro from '@tarojs/taro'

export interface Res {
  code: 0 | 1 | 200
  data: any
  msg: string
}

class AppRequest {
  private interceptorAdded = false

  constructor(
    private BASE_URL: string,
    private TIME_OUT: number
  ) {}

  private interceptor = (chain: Taro.Chain) => {
    const requestParams = chain.requestParams

    // 只处理当前实例的请求（通过URL前缀判断）
    if (!requestParams.url.includes(this.BASE_URL)) {
      return chain.proceed(requestParams)
    }

    // Taro.showLoading({
    //   title: '加载中...',
    // })
    let token = Taro.getStorageSync('__weapp_token__') // 拿到本地缓存中存的token
    if (token && !requestParams.url.includes('/agent/')) {
      requestParams.header = {
        ...requestParams.header,
        Authorization: token.includes('Bearer') ? token : 'Bearer ' + token // 将token添加到头部
      }
    }
    // console.log('requestParams.header', requestParams, requestParams.header)
    return chain
      .proceed(requestParams)
      .then((res: any) => {
        // Taro.hideLoading()
        return res
      })
      .catch((err: any) => {
        // Taro.hideLoading()
        console.error(err)
        return Promise.reject(err)
      })
  }

  private ensureInterceptor() {
    if (!this.interceptorAdded) {
      Taro.addInterceptor(this.interceptor)
      this.interceptorAdded = true
    }
  }

  request<T = any>(options: Taro.request.Option) {
    // 判断url路径是否完整
    let url: string
    if (options.url.includes(this.BASE_URL)) {
      url = options.url
    } else {
      url = this.BASE_URL + options.url
    }

    // 确保拦截器只添加一次
    this.ensureInterceptor()

    return new Promise<T>((resolve, reject) => {
      Taro.request({
        timeout: this.TIME_OUT,
        ...options,
        url,
        success(res) {
          resolve(res.data)
        },
        fail(err) {
          reject(err)
        }
      })
    })
  }
  get<T = Res>(options: Taro.request.Option) {
    return this.request<T>({ ...options, method: 'GET' })
  }
  post<T = Res>(options: Taro.request.Option) {
    return this.request<T>({ ...options, method: 'POST' })
  }
  delete<T = Res>(options: Taro.request.Option) {
    return this.request<T>({ ...options, method: 'DELETE' })
  }
  put<T = Res>(options: Taro.request.Option) {
    return this.request<T>({ ...options, method: 'PUT' })
  }
}

export default AppRequest
