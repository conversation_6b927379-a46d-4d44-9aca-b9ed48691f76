import useObjState from '@/hooks/useObjState'
import { Input, Image } from '@tarojs/components'
import Slice77Img from '@/assets/images/input/slice-77.png'
import Slice78Img from '@/assets/images/input/slice-78.png'
import Slice79Img from '@/assets/images/input/slice-79.png'
import Slice80Img from '@/assets/images/input/slice-80.png'

export default function Keyboard({
  sendMessage,
  chooseImage,
  inputType
}: {
  sendMessage: (message: string) => void
  chooseImage: (type: string[]) => void
  inputType: any
}) {
  const keyword = useObjState('')

  return (
    <div className="px-[40px]">
      <div className="h-[112px] rounded-[24px] bg-white shadow-[0_20px_40px_0_rgba(48,48,48,0.08)] flex items-center justify-between relative">
        <div onClick={() => chooseImage(['camera'])} className="ml-[24px] mr-[8px] flex_center">
          <Image className="w-[48px] h-[48px]" src={Slice77Img} />
        </div>
        <div className="flex-1 h-full">
          <Input
            className="w-full h-[112px] flex items-center outline-none border-none font-normal text-[24px] text-[#2A3447] leading-[32px] not-italic normal-case"
            type="text"
            confirm-type="send"
            placeholder="输入需求生成 T 恤设计"
            name="message"
            value={keyword.val}
            onInput={(e) => {
              keyword.set(e.detail.value)
            }}
            // 回车提交表单
            onConfirm={() => {
              sendMessage(keyword.val)
              keyword.set('') // 清空输入框
            }}
          />
        </div>
        <div onClick={() => inputType.set((v) => (v === 'text' ? 'record' : 'text'))} className="ml-[8px] flex_center">
          <Image className="w-[48px] h-[48px]" src={inputType.val === 'text' ? Slice79Img : Slice80Img} />
        </div>
        <div onClick={() => chooseImage(['album'])} className="mr-[24px] ml-[16px] flex_center">
          <Image className="w-[48px] h-[48px]" src={Slice78Img} />
        </div>
      </div>
    </div>
  )
}
